{"name": "cambioml-frontend-challenge", "version": "1.0.0", "private": true, "description": "Modern AI-powered voice chat application for CambioML Frontend Challenge", "author": "<PERSON><PERSON> <<EMAIL>> (https://github.com/chirag127)", "license": "MIT", "homepage": "https://github.com/chirag127/Frontend-Engineer-1/tree/main/cambioml-frontend-challenge", "repository": {"type": "git", "url": "https://github.com/chirag127/Frontend-Engineer-1.git", "directory": "cambioml-frontend-challenge"}, "bugs": {"url": "https://github.com/chirag127/Frontend-Engineer-1/issues"}, "keywords": ["ai", "voice-chat", "nextjs", "typescript", "gemini", "openai", "frontend-challenge"], "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@google/genai": "^1.10.0", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.6", "lucide-react": "^0.525.0", "next": "15.4.2", "openai": "^5.10.1", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}