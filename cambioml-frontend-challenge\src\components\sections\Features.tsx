'use client';

import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Mic, 
  Brain, 
  Zap, 
  Shield, 
  Globe, 
  Settings,
  MessageSquare,
  Headphones,
  Cpu
} from 'lucide-react';

const Features = () => {
  const features = [
    {
      icon: <Mic className="w-8 h-8" />,
      title: "Advanced Voice Recognition",
      description: "State-of-the-art speech-to-text technology with noise cancellation and echo suppression for crystal-clear audio capture.",
      color: "from-blue-500 to-blue-600"
    },
    {
      icon: <Brain className="w-8 h-8" />,
      title: "Dual AI Intelligence",
      description: "Seamlessly switch between Google Gemini and OpenAI models, each optimized for different conversation styles and use cases.",
      color: "from-purple-500 to-purple-600"
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: "Real-time Processing",
      description: "Lightning-fast response times with streaming audio processing for natural, uninterrupted conversations.",
      color: "from-yellow-500 to-orange-500"
    },
    {
      icon: <MessageSquare className="w-8 h-8" />,
      title: "Natural Conversations",
      description: "Engage in fluid, context-aware dialogues that feel natural and intuitive, just like talking to a human.",
      color: "from-green-500 to-green-600"
    },
    {
      icon: <Headphones className="w-8 h-8" />,
      title: "High-Quality Audio",
      description: "Premium text-to-speech synthesis with multiple voice options and adjustable speaking rates for optimal listening experience.",
      color: "from-pink-500 to-rose-500"
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: "Privacy & Security",
      description: "Your conversations are processed securely with enterprise-grade encryption and no data storage on our servers.",
      color: "from-indigo-500 to-indigo-600"
    },
    {
      icon: <Globe className="w-8 h-8" />,
      title: "Multi-language Support",
      description: "Communicate in multiple languages with automatic language detection and seamless translation capabilities.",
      color: "from-teal-500 to-cyan-500"
    },
    {
      icon: <Settings className="w-8 h-8" />,
      title: "Customizable Experience",
      description: "Personalize your AI interaction with adjustable settings for voice, response style, and conversation preferences.",
      color: "from-gray-500 to-gray-600"
    },
    {
      icon: <Cpu className="w-8 h-8" />,
      title: "Edge Computing",
      description: "Optimized for performance with edge computing capabilities, ensuring low latency and high availability worldwide.",
      color: "from-red-500 to-red-600"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <section id="features" className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Powerful Features for
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"> Modern AI</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Experience cutting-edge AI technology with features designed for seamless, 
            intelligent voice interactions that adapt to your needs.
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {features.map((feature, index) => (
            <motion.div key={feature.title} variants={itemVariants}>
              <Card className="h-full hover:shadow-xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm group">
                <CardHeader className="pb-4">
                  <div className={`w-16 h-16 rounded-xl bg-gradient-to-r ${feature.color} flex items-center justify-center text-white mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    {feature.icon}
                  </div>
                  <CardTitle className="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                    {feature.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default Features;
