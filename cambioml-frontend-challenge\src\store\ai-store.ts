import { create } from 'zustand';

export type AIProvider = 'gemini' | 'openai';

export interface AIState {
  // Provider configuration
  aiProvider: AIProvider;
  
  // Recording state
  isRecording: boolean;
  isProcessing: boolean;
  isSpeaking: boolean;
  
  // Conversation state
  transcript: string;
  error: string | null;
  
  // Audio state
  audioLevel: number;
  
  // Actions
  setAIProvider: (provider: AIProvider) => void;
  setIsRecording: (recording: boolean) => void;
  setIsProcessing: (processing: boolean) => void;
  setIsSpeaking: (speaking: boolean) => void;
  setTranscript: (transcript: string) => void;
  setError: (error: string | null) => void;
  setAudioLevel: (level: number) => void;
  reset: () => void;
}

const initialState = {
  aiProvider: (process.env.NEXT_PUBLIC_AI_PROVIDER as AIProvider) || 'gemini',
  isRecording: false,
  isProcessing: false,
  isSpeaking: false,
  transcript: '',
  error: null,
  audioLevel: 0,
};

export const useAIStore = create<AIState>((set) => ({
  ...initialState,
  
  setAIProvider: (provider) => set({ aiProvider: provider }),
  setIsRecording: (recording) => set({ isRecording: recording }),
  setIsProcessing: (processing) => set({ isProcessing: processing }),
  setIsSpeaking: (speaking) => set({ isSpeaking: speaking }),
  setTranscript: (transcript) => set({ transcript }),
  setError: (error) => set({ error }),
  setAudioLevel: (level) => set({ audioLevel: level }),
  reset: () => set(initialState),
}));
