Excellent! Thank you for the confirmation. By moving forward with the suggested answers, we have a clear and strong direction for this project.

I will now generate the `masterplan.md` file. This document will serve as the complete blueprint for building your application, incorporating all the decisions we've just made. It is designed to be comprehensive enough for an AI code assistant to understand and implement the final product directly.

Here is your masterplan:

***

# Masterplan for CambioML Frontend Challenge

**Document Version:** 1.0
**Owner:** <PERSON><PERSON>
**Status:** final
**Prepared for:** augment code assistant
**Prepared by:** <PERSON><PERSON>

---

## Project Overview
This document outlines the plan for creating a modern, interactive web application inspired by the `energent.ai` website. The project's core is a high-fidelity landing page built with Next.js, TypeScript, and Tailwind CSS. The standout feature is an "AI Integration Hub" that provides a real-time voice chat experience. This hub will integrate with both Google Gemini and OpenAI, with the active provider being selectable via an environment variable at build time. The final application will be deployed to Vercel.

## Project Goals
- To develop a visually appealing, professional, and responsive user interface that mirrors the design aesthetic of `energent.ai`.
- To successfully implement a complex AI-powered feature, specifically a voice chat interface.
- To integrate two separate AI provider backends (Google Gemini and OpenAI) into a single, cohesive user experience.
- To demonstrate strong frontend development skills, including best practices in project architecture, code quality, and state management.
- To deliver a fully deployed and functional web application on Vercel, complete with comprehensive documentation.

## Technical Stack
- **Frontend**: Next.js (React 18+ with TypeScript)
- **Styling**: Tailwind CSS, supplemented with Shadcn/UI for component primitives.
- **Animation**: Framer Motion
- **State Management**: Zustand
- **Backend**: Next.js API Routes (for proxying AI service requests)
- **Database**: Not applicable
- **Deployment**: Vercel

## Project Scope
### In Scope
- A multi-section landing page including a Hero Section and a Features/How-it-Works Section.
- An AI Integration Hub for voice-based interaction with an AI.
- Integration with Google Gemini's Live API for streaming voice chat.
- Integration with OpenAI's APIs (Whisper for Speech-to-Text and TTS for Text-to-Speech).
- A build-time switch using an environment variable to select between Gemini and OpenAI as the active AI provider.
- A fully responsive design that works on desktop, tablet, and mobile devices.
- Deployment of the final application to a public Vercel URL.
- Comprehensive project documentation, including a `README.md` and a demo video.

### Out of Scope
- User authentication, user accounts, and login/signup functionality.
- Persistence of user data or chat history.
- A full, one-to-one replication of every section on the `energent.ai` website.
- A user-facing UI control to switch between AI providers in real-time.
- Any backend database integration.

## Functional Requirements

### Feature Area 1: Main Landing Page
- **FR1.1:** The application must have a main layout including a persistent navigation bar and footer.
- **FR1.2:** The navigation bar must contain links that scroll to the relevant sections of the page (e.g., Features, AI Hub).
- **FR1.3:** The application must feature a compelling Hero Section with a clear headline, subheading, and a call-to-action, animated with Framer Motion.
- **FR1.4:** The application must include a "Features" or "How it Works" section that displays key product points in a clean, card-based layout.

### Feature Area 2: AI Integration Hub
- **FR2.1:** The hub must allow the user to initiate a voice chat session by pressing a button.
- **FR2.2:** The application must request and handle microphone permissions from the user.
- **FR2.3:** The user's speech must be captured and sent for real-time (or near real-time) transcription.
- **FR2.4:** The transcribed text must be sent to the configured AI service (Gemini or OpenAI).
- **FR2.5:** The AI's text response must be received and converted to speech.
- **FR2.6:** The generated audio response must be played back to the user.
- **FR2.7:** The UI must clearly indicate the application's state (e.g., idle, listening, processing, speaking).

### Feature Area 3: AI Provider Switching
- **FR3.1:** The application's AI provider must be configurable via a `NEXT_PUBLIC_AI_PROVIDER` environment variable.
- **FR3.2:** If the provider is set to `gemini`, the application will use the Google Gemini Live API.
- **FR3.3:** If the provider is set to `openai`, the application will use the OpenAI Whisper and TTS APIs.

## Non-Functional Requirements
- **Performance**: The application must have fast load times, optimized through Next.js features like SSG and component lazy loading. Lighthouse scores should be high.
- **Scalability**: While the app has no database, the backend proxy (Next.js API routes) should be stateless to scale effectively on Vercel's serverless infrastructure.
- **Maintainability**: Code must be clean, modular, and well-commented. A clear project structure must be maintained.
- **UI/UX**: The UI must be modern, intuitive, and visually aligned with the `energent.ai` brand. It should incorporate subtle, meaningful animations to enhance the user experience.
- **Responsiveness**: All components and layouts must adapt seamlessly to screen sizes from 360px (mobile) to 1920px (desktop) and beyond.
- **Security**: API keys for AI services must not be exposed on the client-side. They will be stored as environment variables and accessed exclusively through a backend API proxy.
- **Documentation**: A comprehensive `README.md` will be provided, including setup instructions, architecture decisions, and a link to the deployed site.

## Implementation Plan

This section outlines the detailed implementation plan, broken down into logical phases and tasks.

### Phase 1: Setup & Foundation
- **Task 1:** Initialize a new Next.js project using `create-next-app` with TypeScript and Tailwind CSS.
- **Task 2:** Set up the project directory structure: `/app`, `/components` (with subfolders for `ui`, `sections`, `icons`), `/lib`, `/hooks`, and `/store`.
- **Task 3:** Configure `eslint` and `prettier` for consistent code quality.
- **Task 4:** Install project dependencies: `zustand` for state management, `framer-motion` for animations, `lucide-react` for icons.
- **Task 5:** Set up and configure `shadcn/ui` by running its init command.
- **Task 6:** Create the `.env.example` file with `NEXT_PUBLIC_AI_PROVIDER=gemini`, `GEMINI_API_KEY=`, and `OPENAI_API_KEY=`.

### Phase 2: Core UI & Component Development
- **Task 1:** Create the main `app/layout.tsx` file, defining the root layout with font and metadata configurations.
- **Task 2:** Build a reusable `Navbar` component (`/components/sections/Navbar.tsx`) with placeholder navigation links.
- **Task 3:** Build a reusable `Footer` component (`/components/sections/Footer.tsx`).
- **Task 4:** Develop the `Hero` section (`/components/sections/Hero.tsx`), using Framer Motion for text and button entrance animations.
- **Task 5:** Develop the `Features` section (`/components/sections/Features.tsx`), using a grid layout and card components.
- **Task 6:** Assemble the main page (`app/page.tsx`) using the created sections.
- **Task 7:** Iteratively test and refine the responsiveness of all components using Tailwind's breakpoint system.

### Phase 3: AI Hub Implementation & State Management
- **Task 1:** Design and build the UI for the `AIHub` section (`/components/sections/AIHub.tsx`), including a primary action button and status indicators.
- **Task 2:** Create a Zustand store (`/store/ai-store.ts`) to manage AI interaction state: `isRecording`, `isProcessing`, `aiProvider`, `chatTranscript`, etc.
- **Task 3:** Develop a custom hook `useMicrophone` (`/hooks/useMicrophone.ts`) to handle browser microphone permissions and audio stream capture.
- **Task 4:** Create the backend API proxy endpoint at `app/api/ai-chat/route.ts`. This endpoint will read the audio data from the request.
- **Task 5:** Implement the core logic in the API route to check the `process.env.NEXT_PUBLIC_AI_PROVIDER` variable and branch to the appropriate AI service handler.

### Phase 4: AI Service Integration
- **Task 1: Gemini Integration**
    - Within the API proxy, create a handler function for Gemini.
    - Adapt the logic from the `live-api-web-console` to work server-side within the Next.js API route, using the `GEMINI_API_KEY`.
    - Implement streaming logic to send audio data to Gemini and stream the response back to the client.
- **Task 2: OpenAI Integration**
    - Within the API proxy, create a handler function for OpenAI.
    - Install the `openai` Node.js library.
    - When audio data is received, use `openai.audio.transcriptions.create({ model: "whisper-1", file: ... })` to get the text.
    - Send the transcribed text to `openai.chat.completions.create({ model: "gpt-4o", messages: ... })`.
    - Take the text response and convert it back to audio using `openai.audio.speech.create({ model: "tts-1", voice: "alloy", input: ... })`.
    - Stream the resulting audio back to the client.
- **Task 3: Client-Side Handling**
    - Connect the `AIHub` component to the `useMicrophone` hook and the Zustand store.
    - When the user records, send the captured audio to the `/api/ai-chat` endpoint.
    - Handle the streaming response from the API and play the audio using the browser's `Audio` API.
    - Update the UI based on the state from the Zustand store.

### Phase 5: Testing, Deployment & Documentation
- **Task 1:** Conduct thorough end-to-end testing of the voice chat flow for both Gemini and OpenAI configurations.
- **Task 2:** Test the application's responsiveness across Chrome, Firefox, and Safari on desktop and mobile viewports.
- **Task 3:** Create a new private GitHub repository and push the code.
- **Task 4:** Connect the GitHub repository to a new Vercel project.
- **Task 5:** Configure the environment variables (`NEXT_PUBLIC_AI_PROVIDER`, `GEMINI_API_KEY`, `OPENAI_API_KEY`) in the Vercel project settings.
- **Task 6:** Trigger a deployment and verify the deployed application functions correctly.
- **Task 7:** Write the `README.md` file, including author info, project overview, setup instructions, a Vercel link, and mentions for reviewers.
- **Task 8:** Record a 3-minute demo video showcasing the UI, responsiveness, and the full AI voice chat functionality.

## API Endpoints (if applicable)
- `POST /api/ai-chat`
  - **Description**: Acts as a secure proxy for all AI interactions. It receives audio data from the client, routes it to the configured cloud AI provider (Gemini or OpenAI), and streams the audio response back.
  - **Request Body**: `FormData` containing the audio blob.
  - **Response Body**: A streaming audio response (`audio/mpeg` or similar).

## Data Models (if applicable)
### Zustand AI Store (`AIState`)
- `aiProvider`: `'gemini' | 'openai'` - The currently active AI provider, read from env vars.
- `isRecording`: `boolean` - True if the microphone is currently capturing audio.
- `isProcessing`: `boolean` - True if the app is waiting for a response from the AI.
- `isSpeaking`: `boolean` - True if the AI response audio is currently playing.
- `transcript`: `string` - The latest transcript of user or AI speech.
- `error`: `string | null` - Stores any error messages.

## Project Structure
```
project-root/
├── app/
│   ├── api/
│   │   └── ai-chat/
│   │       └── route.ts        # Backend proxy for AI services
│   ├── favicon.ico
│   ├── globals.css
│   ├── layout.tsx              # Root layout
│   └── page.tsx                # Main page component
├── components/
│   ├── icons/                  # Custom icon components
│   ├── sections/               # Page sections (Hero, Features, AIHub, etc.)
│   └── ui/                     # Shadcn-UI components (Button, Card, etc.)
├── hooks/
│   └── useMicrophone.ts        # Hook for microphone access
├── lib/
│   └── utils.ts                # Tailwind CSS merge utility
├── public/
│   └── [images, etc.]
├── store/
│   └── ai-store.ts             # Zustand store for AI state
├── .env.example                # Example environment variables
├── .eslintrc.json
├── .gitignore
├── next.config.mjs
├── package.json
├── postcss.config.js
├── README.md
├── tailwind.config.ts
└── tsconfig.json
```

## Environment Variables
```
# Required environment variables

# Specifies the AI provider to use. Can be 'gemini' or 'openai'.
NEXT_PUBLIC_AI_PROVIDER=gemini

# Your secret API key for the Google Gemini API.
# Used server-side only.
GEMINI_API_KEY=your_gemini_api_key_here

# Your secret API key for the OpenAI API.
# Used server-side only.
OPENAI_API_KEY=your_openai_api_key_here
```

## Testing Strategy
The testing approach will be primarily manual and end-to-end, focusing on user experience and feature correctness.
1.  **Component Testing**: Manually verify that all UI components render correctly and are responsive.
2.  **Feature Testing**: Test the full voice chat flow: click button -> grant permission -> speak -> see processing state -> hear response. This will be performed for both `gemini` and `openai` configurations by changing the local `.env` file.
3.  **Cross-Browser Testing**: Manually test the deployed application on the latest versions of Chrome, Firefox, and Safari to ensure compatibility.
4.  **Responsive Testing**: Use browser developer tools to simulate various device viewports (e.g., iPhone 12, iPad, large desktop).

## Deployment Strategy
The application will be deployed to Vercel via its official Git integration.
1.  The `main` branch of the GitHub repository will be connected to the Vercel project.
2.  Continuous Deployment is enabled: every push to the `main` branch will automatically trigger a new production build and deployment.
3.  All required environment variables will be securely stored in the Vercel project's settings, not committed to the repository.

## Maintenance Plan
As this is a project for a coding challenge, no long-term maintenance plan is required. Documentation and clean code will be prioritized to ensure the project is easy to understand and review.

## Risks and Mitigations
| Risk | Impact | Likelihood | Mitigation |
| :--- | :--- | :--- | :--- |
| Real-time voice streaming is complex | High | Medium | Start with the provided Gemini example. For OpenAI, if full streaming proves too difficult within the timeframe, fall back to a simpler request-response pattern (record full audio, then send for processing). |
| Matching `energent.ai` style is time-consuming | Medium | Low | Use Shadcn/UI to accelerate component building. Focus on capturing the overall aesthetic, color scheme, and layout rather than a pixel-perfect clone. |
| AI API returns unexpected errors or latency | Medium | Medium | Implement robust error handling in the API proxy and on the client-side. Display clear error messages to the user. Use loading indicators to manage user perception of latency. |

## Future Enhancements
- A UI toggle to allow users to switch between AI providers in real-time.
- Storing chat history in the browser's `localStorage`.
- Expanding the landing page to include more sections from `energent.ai` (e.g., FAQ, Pricing).
- Implementing more sophisticated animations and micro-interactions.

## Development Guidelines
### Code Quality & Design Principles
- Adhere to industry-standard best practices: clean code, modularity, and comprehensive error handling.
- Apply SOLID, DRY (Don't Repeat Yourself), and KISS (Keep It Simple, Stupid) principles.
- Design modular, reusable components and functions.
- Optimize for code readability with a maintainable structure. Add concise, useful comments where logic is complex.

### Frontend Development
- Create a modern, clean, and intuitive UI design inspired by `energent.ai`.
- Adhere to core UI/UX principles: clarity, consistency, simplicity, user feedback, and accessibility (WCAG).
- Use Tailwind CSS for styling, following a utility-first approach.

### Data Handling & APIs
- Integrate with live AI APIs as specified. No mock data in the final product.
- Manage all credentials and API keys exclusively via server-side environment variables, accessed through a `.env` file locally and Vercel's environment variables in production.
- Centralize the API proxy endpoint URL in a constants file to avoid hardcoding.

## Tool Usage Instructions
### MCP Servers and Tools
- Use the `context7` MCP server to gather contextual information about relevant libraries, frameworks, and APIs (e.g., Next.js, Vercel, Gemini API).
- Use the `clear_thought` MCP servers (e.g., `designpattern`, `debuggingapproach`) for systematic problem-solving and architectural decisions.
- Use the `websearch` tool to find information on the internet for resolving technical issues or finding documentation.

### System & Environment Considerations
- Use language-native path manipulation libraries (e.g., Node.js `path`) for robust path handling.
- Use `npm`, `yarn`, or `pnpm` commands via the `launch-process` tool to manage dependencies. Do not edit `package.json` directly.

### Error Handling & Debugging
- First, attempt to resolve errors autonomously using available tools.
- Perform systematic debugging: consult web resources, documentation, modify code, adjust configuration, and retry.
- Report back only if an insurmountable blocker persists after exhausting all self-correction efforts.

## Conclusion
This masterplan provides a comprehensive roadmap for developing the CambioML Frontend Challenge project. By adhering to this plan, we can efficiently build a high-quality, feature-rich, and visually impressive application that meets all specified requirements. The key to success will be a phased implementation, robust security practices for API keys, and a strong focus on user experience.