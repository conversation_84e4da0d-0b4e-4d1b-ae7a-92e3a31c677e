import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "CambioML Frontend Challenge - AI Voice Chat",
  description: "Modern AI-powered voice chat application inspired by energent.ai",
  keywords: ["AI", "voice chat", "Gemini", "OpenAI", "Next.js", "TypeScript"],
  authors: [{ name: "<PERSON><PERSON>", url: "https://github.com/chirag127" }],
  creator: "<PERSON><PERSON>",
  openGraph: {
    title: "CambioML Frontend Challenge",
    description: "Modern AI-powered voice chat application",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.className} antialiased`}>
        {children}
      </body>
    </html>
  );
}
