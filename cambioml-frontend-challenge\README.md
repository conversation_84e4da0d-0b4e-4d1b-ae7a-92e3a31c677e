# CambioML Frontend Challenge - AI Voice Chat Application

**Author:** <PERSON><PERSON> ([chirag127](https://github.com/chirag127))
**Created:** July 20, 2025 07:22:34 UTC
**Version:** 1.0.0
**License:** MIT

## 🚀 Project Overview

This is a modern, interactive web application built for the CambioML Frontend Challenge. It features a high-fidelity landing page inspired by energent.ai with a standout "AI Integration Hub" that provides real-time voice chat experiences powered by both Google Gemini and OpenAI.

### 🌟 Key Features

-   **Modern Landing Page**: Clean, responsive design inspired by energent.ai
-   **AI Voice Chat**: Real-time voice conversations with AI
-   **Dual AI Integration**: Switch between Google Gemini and OpenAI
-   **Responsive Design**: Works seamlessly on desktop, tablet, and mobile
-   **Real-time Audio Processing**: Advanced speech recognition and synthesis
-   **Professional UI/UX**: Built with Tailwind CSS and Framer Motion animations

## 🛠 Tech Stack

-   **Frontend Framework**: Next.js 14 with TypeScript
-   **Styling**: Tailwind CSS + Shadcn/UI components
-   **Animations**: Framer Motion
-   **State Management**: Zustand
-   **AI Integration**: Google Gemini API + OpenAI API
-   **Audio Processing**: Web Audio API + MediaRecorder API
-   **Deployment**: Vercel

## 📋 Prerequisites

Before running this project, ensure you have:

-   Node.js 18+ installed
-   npm or yarn package manager
-   API keys for Google Gemini and/or OpenAI
-   Modern web browser with microphone support

## 🔧 Installation & Setup

### 1. Clone the Repository

```bash
git clone https://github.com/chirag127/Frontend-Engineer-1.git
cd Frontend-Engineer-1/cambioml-frontend-challenge
```

### 2. Install Dependencies

```bash
npm install
# or
yarn install
```

### 3. Environment Configuration

Create a `.env.local` file in the root directory:

```env
# AI Provider Configuration (choose 'gemini' or 'openai')
NEXT_PUBLIC_AI_PROVIDER=openai

# API Keys
GEMINI_API_KEY=your_gemini_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
```

**Getting API Keys:**

-   **Google Gemini**: Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
-   **OpenAI**: Visit [OpenAI API Keys](https://platform.openai.com/api-keys)

### 4. Run the Development Server

```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🎯 Usage

### Voice Chat Interface

1. **Grant Permissions**: Click the microphone button and allow microphone access
2. **Start Recording**: Click the blue microphone button to start recording
3. **Stop Recording**: Click the red button again to stop and process your message
4. **Listen to Response**: The AI will respond with synthesized speech

### AI Provider Switching

The application uses the AI provider specified in your environment variables:

-   Set `NEXT_PUBLIC_AI_PROVIDER=gemini` for Google Gemini
-   Set `NEXT_PUBLIC_AI_PROVIDER=openai` for OpenAI

## 📁 Project Structure

```
cambioml-frontend-challenge/
├── src/
│   ├── app/                    # Next.js app directory
│   │   ├── api/ai-chat/       # AI chat API endpoint
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Main page
│   ├── components/
│   │   ├── sections/          # Page sections
│   │   │   ├── AIHub.tsx      # AI voice chat interface
│   │   │   ├── Features.tsx   # Features section
│   │   │   ├── Footer.tsx     # Footer component
│   │   │   ├── Hero.tsx       # Hero section
│   │   │   └── Navbar.tsx     # Navigation bar
│   │   └── ui/                # Shadcn/UI components
│   ├── hooks/
│   │   └── useMicrophone.ts   # Microphone management hook
│   ├── lib/
│   │   ├── ai/                # AI service handlers
│   │   │   ├── gemini-handler.ts
│   │   │   └── openai-handler.ts
│   │   └── utils.ts           # Utility functions
│   └── store/
│       └── ai-store.ts        # Zustand state management
├── public/                    # Static assets
├── .env.example              # Environment variables template
├── .env.local               # Local environment variables
├── package.json             # Dependencies and scripts
└── README.md               # This file
```

## 🔧 API Documentation

### POST /api/ai-chat

Processes audio input and returns AI-generated audio response.

**Request:**

-   Method: POST
-   Content-Type: multipart/form-data
-   Body:
    -   `audio`: Audio file (WebM format)
    -   `provider`: AI provider ('gemini' or 'openai')

**Response:**

-   Content-Type: audio/mpeg
-   Body: Audio stream

## 🎨 Design Principles

This application follows modern UI/UX principles:

-   **Clarity**: Clean, intuitive interface
-   **Consistency**: Uniform design patterns throughout
-   **Accessibility**: WCAG compliant with proper ARIA labels
-   **Responsiveness**: Mobile-first responsive design
-   **Performance**: Optimized loading and smooth animations

## 🚀 Deployment

### Vercel Deployment (Recommended)

1. **Connect Repository**: Link your GitHub repository to Vercel
2. **Configure Environment Variables**: Add your API keys in Vercel dashboard
3. **Deploy**: Automatic deployment on every push to main branch

### Manual Deployment

```bash
npm run build
npm start
```

## 🧪 Testing

### Manual Testing Checklist

-   [ ] Microphone permission request works
-   [ ] Recording starts and stops correctly
-   [ ] Audio processing completes successfully
-   [ ] AI response plays back correctly
-   [ ] Error handling displays appropriate messages
-   [ ] Responsive design works on different screen sizes
-   [ ] Both AI providers function correctly

### Browser Compatibility

-   ✅ Chrome 90+
-   ✅ Firefox 88+
-   ✅ Safari 14+
-   ✅ Edge 90+

## 🔒 Security Considerations

-   API keys are stored server-side only
-   No audio data is permanently stored
-   HTTPS required for microphone access
-   Environment variables protect sensitive credentials

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

-   **CambioML** for the frontend challenge opportunity
-   **Google** for the Gemini API
-   **OpenAI** for the GPT and Whisper APIs
-   **Vercel** for the deployment platform
-   **Energent.ai** for design inspiration

## 📞 Contact

**Chirag Singhal**

-   GitHub: [@chirag127](https://github.com/chirag127)
-   LinkedIn: [chirag127](https://linkedin.com/in/chirag127)

---

**Built with ❤️ for the CambioML Frontend Challenge**
