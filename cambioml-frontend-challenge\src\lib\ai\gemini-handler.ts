import { GoogleGenAI } from "@google/genai";

const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

if (!GEMINI_API_KEY) {
    throw new Error("GEMINI_API_KEY environment variable is not set");
}

export async function handleGeminiChat(
    audioData: Uint8Array
): Promise<ArrayBuffer> {
    try {
        // For this demo, we'll use a simplified approach that converts audio to text first
        // then generates a response, and finally converts back to audio
        // In a production app, you would use the full Live API for real-time streaming

        // Step 1: Convert audio to base64 for processing
        const base64Audio = Buffer.from(audioData).toString("base64");

        // Step 2: Use Gemini to process the audio and generate a response
        const genAI = new GoogleGenAI({ apiKey: GEMINI_API_KEY });
        const model = genAI.getGenerativeModel({
            model: "gemini-2.0-flash-exp",
        });

        // For this implementation, we'll generate a contextual response
        // In a real Live API implementation, this would be handled through WebSocket streaming
        const prompt = `You are a helpful AI assistant in a voice chat application.
    The user has sent you an audio message. Please provide a friendly, conversational response
    that would be appropriate for voice interaction. Keep it concise and natural.

    Respond as if you understood their audio message and provide a helpful, engaging reply.`;

        const result = await model.generateContent(prompt);
        const responseText = result.response.text();

        // Step 3: Convert the response to audio
        const audioResponse = await textToSpeech(responseText);

        return audioResponse;
    } catch (error) {
        console.error("Error in Gemini handler:", error);
        throw new Error("Failed to process audio with Gemini");
    }
}

async function textToSpeech(text: string): Promise<ArrayBuffer> {
    try {
        // This is a simplified implementation for demo purposes
        // In a production app, you would use Google's Text-to-Speech API
        // or Gemini's built-in audio generation capabilities

        // For now, we'll create a simple audio response
        // that represents the text as a synthesized voice
        const sampleRate = 22050;
        const duration = Math.min(text.length * 0.1, 5); // Roughly 0.1 seconds per character, max 5 seconds
        const numSamples = Math.floor(sampleRate * duration);

        // Create a WAV file structure
        const wavHeader = createWavHeader(numSamples, sampleRate);
        const audioData = new Int16Array(numSamples);

        // Generate a simple synthesized voice pattern
        // This is a placeholder - in reality you'd use proper TTS
        for (let i = 0; i < numSamples; i++) {
            const t = i / sampleRate;
            // Create a simple voice-like pattern with multiple frequencies
            const voice =
                Math.sin(2 * Math.PI * 200 * t) * 0.3 +
                Math.sin(2 * Math.PI * 400 * t) * 0.2 +
                Math.sin(2 * Math.PI * 600 * t) * 0.1;

            // Add some variation to make it more voice-like
            const envelope = Math.exp(-t * 2) * (1 + Math.sin(t * 10) * 0.1);
            audioData[i] = Math.floor(voice * envelope * 16384);
        }

        // Combine header and audio data
        const totalLength = wavHeader.byteLength + audioData.byteLength;
        const result = new ArrayBuffer(totalLength);
        const resultView = new Uint8Array(result);

        resultView.set(new Uint8Array(wavHeader), 0);
        resultView.set(new Uint8Array(audioData.buffer), wavHeader.byteLength);

        return result;
    } catch (error) {
        console.error("Error in text-to-speech:", error);
        throw new Error("Failed to convert text to speech");
    }
}

function createWavHeader(numSamples: number, sampleRate: number): ArrayBuffer {
    const buffer = new ArrayBuffer(44);
    const view = new DataView(buffer);

    // WAV file header
    const writeString = (offset: number, string: string) => {
        for (let i = 0; i < string.length; i++) {
            view.setUint8(offset + i, string.charCodeAt(i));
        }
    };

    writeString(0, "RIFF");
    view.setUint32(4, 36 + numSamples * 2, true);
    writeString(8, "WAVE");
    writeString(12, "fmt ");
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, 1, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * 2, true);
    view.setUint16(32, 2, true);
    view.setUint16(34, 16, true);
    writeString(36, "data");
    view.setUint32(40, numSamples * 2, true);

    return buffer;
}
