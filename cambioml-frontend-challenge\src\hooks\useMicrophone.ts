import { useCallback, useRef, useState } from "react";

export interface UseMicrophoneOptions {
    onAudioData?: (audioData: Blob) => void;
    onVolumeChange?: (volume: number) => void;
    onError?: (error: Error) => void;
}

export interface UseMicrophoneReturn {
    isRecording: boolean;
    hasPermission: boolean;
    startRecording: () => Promise<void>;
    stopRecording: () => void;
    forceStop: () => void;
    requestPermission: () => Promise<boolean>;
}

export const useMicrophone = (
    options: UseMicrophoneOptions = {}
): UseMicrophoneReturn => {
    const [isRecording, setIsRecording] = useState(false);
    const [hasPermission, setHasPermission] = useState(false);

    const mediaRecorderRef = useRef<MediaRecorder | null>(null);
    const streamRef = useRef<MediaStream | null>(null);
    const audioContextRef = useRef<AudioContext | null>(null);
    const analyserRef = useRef<AnalyserNode | null>(null);
    const volumeIntervalRef = useRef<NodeJS.Timeout | null>(null);

    const requestPermission = useCallback(async (): Promise<boolean> => {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true,
                },
            });

            // Test that we got the stream and then stop it
            stream.getTracks().forEach((track) => track.stop());
            setHasPermission(true);
            return true;
        } catch (error) {
            console.error("Microphone permission denied:", error);
            setHasPermission(false);
            options.onError?.(error as Error);
            return false;
        }
    }, [options]);

    const startVolumeMonitoring = useCallback(
        (stream: MediaStream) => {
            if (!options.onVolumeChange) return;

            try {
                audioContextRef.current = new AudioContext();
                analyserRef.current = audioContextRef.current.createAnalyser();
                const source =
                    audioContextRef.current.createMediaStreamSource(stream);

                analyserRef.current.fftSize = 256;
                source.connect(analyserRef.current);

                const dataArray = new Uint8Array(
                    analyserRef.current.frequencyBinCount
                );

                const updateVolume = () => {
                    if (!analyserRef.current) return;

                    analyserRef.current.getByteFrequencyData(dataArray);
                    const average =
                        dataArray.reduce((sum, value) => sum + value, 0) /
                        dataArray.length;
                    const volume = average / 255; // Normalize to 0-1

                    options.onVolumeChange?.(volume);
                };

                volumeIntervalRef.current = setInterval(updateVolume, 100);
            } catch (error) {
                console.error("Error setting up volume monitoring:", error);
            }
        },
        [options]
    );

    const stopVolumeMonitoring = useCallback(() => {
        if (volumeIntervalRef.current) {
            clearInterval(volumeIntervalRef.current);
            volumeIntervalRef.current = null;
        }

        if (audioContextRef.current) {
            audioContextRef.current.close();
            audioContextRef.current = null;
        }

        analyserRef.current = null;
        options.onVolumeChange?.(0);
    }, [options]);

    const startRecording = useCallback(async () => {
        if (isRecording) return;

        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true,
                },
            });

            streamRef.current = stream;
            setHasPermission(true);

            // Start volume monitoring
            startVolumeMonitoring(stream);

            const mediaRecorder = new MediaRecorder(stream, {
                mimeType: "audio/webm;codecs=opus",
            });

            const audioChunks: Blob[] = [];

            mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    audioChunks.push(event.data);
                }
            };

            mediaRecorder.onstop = () => {
                console.log("MediaRecorder stopped, processing audio");
                const audioBlob = new Blob(audioChunks, {
                    type: "audio/webm;codecs=opus",
                });
                options.onAudioData?.(audioBlob);
                stopVolumeMonitoring();

                // Clean up stream
                if (streamRef.current) {
                    streamRef.current
                        .getTracks()
                        .forEach((track) => track.stop());
                    streamRef.current = null;
                }

                // Ensure recording state is false
                setIsRecording(false);
                console.log("MediaRecorder cleanup complete");
            };

            mediaRecorder.onerror = (event) => {
                console.error("MediaRecorder error:", event);
                setIsRecording(false);
                options.onError?.(new Error("Recording failed"));
            };

            mediaRecorderRef.current = mediaRecorder;
            mediaRecorder.start(100); // Collect data every 100ms
            setIsRecording(true);
        } catch (error) {
            console.error("Error starting recording:", error);
            options.onError?.(error as Error);
            setHasPermission(false);
            setIsRecording(false);
        }
    }, [isRecording, options, startVolumeMonitoring, stopVolumeMonitoring]);

    const stopRecording = useCallback(() => {
        // Always try to stop, regardless of current state
        console.log("stopRecording called, current state:", isRecording);

        // Stop the media recorder if it exists
        if (mediaRecorderRef.current) {
            console.log("MediaRecorder state:", mediaRecorderRef.current.state);
            if (mediaRecorderRef.current.state === "recording") {
                mediaRecorderRef.current.stop();
                console.log("MediaRecorder stopped");
            }
            mediaRecorderRef.current = null;
        }

        // Stop all tracks and clean up stream
        if (streamRef.current) {
            console.log("Stopping stream tracks");
            streamRef.current.getTracks().forEach((track) => {
                track.stop();
                console.log("Track stopped:", track.kind);
            });
            streamRef.current = null;
        }

        // Stop volume monitoring
        stopVolumeMonitoring();

        // Update state immediately
        setIsRecording(false);
        console.log("Recording state set to false");
    }, [stopVolumeMonitoring]);

    const forceStop = useCallback(() => {
        console.log("Force stopping recording...");

        // Force stop everything regardless of state
        if (mediaRecorderRef.current) {
            try {
                if (mediaRecorderRef.current.state !== "inactive") {
                    mediaRecorderRef.current.stop();
                }
            } catch (error) {
                console.error("Error force stopping MediaRecorder:", error);
            }
            mediaRecorderRef.current = null;
        }

        if (streamRef.current) {
            try {
                streamRef.current.getTracks().forEach((track) => {
                    track.stop();
                });
            } catch (error) {
                console.error("Error stopping tracks:", error);
            }
            streamRef.current = null;
        }

        stopVolumeMonitoring();
        setIsRecording(false);
        console.log("Force stop complete");
    }, [stopVolumeMonitoring]);

    return {
        isRecording,
        hasPermission,
        startRecording,
        stopRecording,
        forceStop,
        requestPermission,
    };
};
