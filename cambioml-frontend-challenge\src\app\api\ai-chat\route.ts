import { NextRequest, NextResponse } from "next/server";
import { handleGeminiChat } from "@/lib/ai/gemini-handler";
import { handleOpenAIChat } from "@/lib/ai/openai-handler";

export async function POST(request: NextRequest) {
    try {
        const formData = await request.formData();
        const audioFile = formData.get("audio") as File;
        const provider = formData.get("provider") as string;

        console.log("AI Chat API called with provider:", provider);

        if (!audioFile) {
            console.error("No audio file provided");
            return NextResponse.json(
                { error: "No audio file provided" },
                { status: 400 }
            );
        }

        if (!provider || !["gemini", "openai"].includes(provider)) {
            console.error("Invalid provider:", provider);
            return NextResponse.json(
                { error: "Invalid or missing AI provider" },
                { status: 400 }
            );
        }

        // Check API keys
        const geminiKey = process.env.GEMINI_API_KEY;
        const openaiKey = process.env.OPENAI_API_KEY;

        if (
            provider === "gemini" &&
            (!geminiKey || geminiKey === "your_gemini_api_key_here")
        ) {
            console.error("Gemini API key not configured");
            return NextResponse.json(
                {
                    error: "Gemini API key not configured. Please set GEMINI_API_KEY in your environment variables.",
                },
                { status: 500 }
            );
        }

        if (
            provider === "openai" &&
            (!openaiKey || openaiKey === "your_openai_api_key_here")
        ) {
            console.error("OpenAI API key not configured");
            return NextResponse.json(
                {
                    error: "OpenAI API key not configured. Please set OPENAI_API_KEY in your environment variables.",
                },
                { status: 500 }
            );
        }

        // Convert audio file to buffer
        const audioBuffer = await audioFile.arrayBuffer();
        const audioData = new Uint8Array(audioBuffer);

        console.log("Audio data size:", audioData.length, "bytes");

        let audioResponse: ArrayBuffer;

        // Route to appropriate AI handler
        if (provider === "gemini") {
            console.log("Using Gemini handler");
            audioResponse = await handleGeminiChat(audioData);
        } else {
            console.log("Using OpenAI handler");
            audioResponse = await handleOpenAIChat(audioData);
        }

        console.log(
            "AI response generated, size:",
            audioResponse.byteLength,
            "bytes"
        );

        // Return audio response
        return new NextResponse(audioResponse, {
            status: 200,
            headers: {
                "Content-Type": "audio/mpeg",
                "Content-Length": audioResponse.byteLength.toString(),
            },
        });
    } catch (error) {
        console.error("Error in AI chat API:", error);

        // Return more specific error information
        const errorMessage =
            error instanceof Error ? error.message : "Internal server error";

        return NextResponse.json(
            {
                error: errorMessage,
                details: "Check server logs for more information",
            },
            { status: 500 }
        );
    }
}
