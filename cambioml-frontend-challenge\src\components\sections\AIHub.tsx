'use client';

import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAIStore } from '@/store/ai-store';
import { useMicrophone } from '@/hooks/useMicrophone';
import { 
  Mic, 
  MicOff, 
  Volume2, 
  VolumeX, 
  Loader2, 
  Brain,
  Sparkles,
  AlertCircle
} from 'lucide-react';

const AIHub = () => {
  const {
    aiProvider,
    isRecording,
    isProcessing,
    isSpeaking,
    transcript,
    error,
    audioLevel,
    setIsRecording,
    setIsProcessing,
    setIsSpeaking,
    setTranscript,
    setError,
    setAudioLevel,
  } = useAIStore();

  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);

  const { startRecording, stopRecording, hasPermission, requestPermission } = useMicrophone({
    onAudioData: (blob) => {
      setAudioBlob(blob);
      handleAudioProcessing(blob);
    },
    onVolumeChange: setAudioLevel,
    onError: (error) => setError(error.message),
  });

  const handleAudioProcessing = async (audioBlob: Blob) => {
    setIsProcessing(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('audio', audioBlob, 'audio.webm');
      formData.append('provider', aiProvider);

      const response = await fetch('/api/ai-chat', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Handle streaming audio response
      const audioArrayBuffer = await response.arrayBuffer();
      const audioBlob = new Blob([audioArrayBuffer], { type: 'audio/mpeg' });
      
      // Play the audio response
      const audioUrl = URL.createObjectURL(audioBlob);
      const audio = new Audio(audioUrl);
      
      setIsSpeaking(true);
      
      audio.onended = () => {
        setIsSpeaking(false);
        URL.revokeObjectURL(audioUrl);
      };
      
      audio.onerror = () => {
        setIsSpeaking(false);
        setError('Failed to play audio response');
        URL.revokeObjectURL(audioUrl);
      };
      
      await audio.play();
      
    } catch (error) {
      console.error('Error processing audio:', error);
      setError(error instanceof Error ? error.message : 'Failed to process audio');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleStartRecording = async () => {
    if (!hasPermission) {
      const granted = await requestPermission();
      if (!granted) {
        setError('Microphone permission is required for voice chat');
        return;
      }
    }

    setError(null);
    setIsRecording(true);
    await startRecording();
  };

  const handleStopRecording = () => {
    setIsRecording(false);
    stopRecording();
  };

  const getStatusText = () => {
    if (error) return 'Error occurred';
    if (isSpeaking) return 'AI is speaking...';
    if (isProcessing) return 'Processing your message...';
    if (isRecording) return 'Listening...';
    return 'Ready to chat';
  };

  const getStatusColor = () => {
    if (error) return 'text-red-600';
    if (isSpeaking) return 'text-green-600';
    if (isProcessing) return 'text-blue-600';
    if (isRecording) return 'text-orange-600';
    return 'text-gray-600';
  };

  return (
    <section id="ai-hub" className="py-24 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            AI Integration Hub
          </h2>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Experience real-time voice conversations with advanced AI. 
            Currently powered by <span className="font-semibold text-blue-400">{aiProvider === 'gemini' ? 'Google Gemini' : 'OpenAI'}</span>.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <Card className="bg-white/10 backdrop-blur-md border-white/20 text-white">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl font-semibold flex items-center justify-center gap-2">
                <Brain className="w-6 h-6 text-blue-400" />
                Voice Chat Interface
              </CardTitle>
              <CardDescription className="text-gray-300">
                Click the microphone to start a voice conversation with AI
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-8">
              {/* Main Control Button */}
              <div className="flex flex-col items-center space-y-6">
                <motion.div
                  className="relative"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={isRecording ? handleStopRecording : handleStartRecording}
                    disabled={isProcessing || isSpeaking}
                    size="lg"
                    className={`w-24 h-24 rounded-full text-white font-semibold transition-all duration-300 ${
                      isRecording 
                        ? 'bg-red-500 hover:bg-red-600 animate-pulse' 
                        : 'bg-blue-500 hover:bg-blue-600'
                    } ${(isProcessing || isSpeaking) ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    {isProcessing ? (
                      <Loader2 className="w-8 h-8 animate-spin" />
                    ) : isSpeaking ? (
                      <Volume2 className="w-8 h-8" />
                    ) : isRecording ? (
                      <MicOff className="w-8 h-8" />
                    ) : (
                      <Mic className="w-8 h-8" />
                    )}
                  </Button>
                  
                  {/* Audio level indicator */}
                  {isRecording && (
                    <motion.div
                      className="absolute inset-0 rounded-full border-4 border-white/30"
                      animate={{
                        scale: [1, 1 + audioLevel * 0.5, 1],
                      }}
                      transition={{
                        duration: 0.3,
                        repeat: Infinity,
                      }}
                    />
                  )}
                </motion.div>

                {/* Status Text */}
                <div className="text-center">
                  <p className={`text-lg font-medium ${getStatusColor()}`}>
                    {getStatusText()}
                  </p>
                  {isRecording && (
                    <p className="text-sm text-gray-400 mt-1">
                      Speak now, release to send
                    </p>
                  )}
                </div>
              </div>

              {/* Error Display */}
              <AnimatePresence>
                {error && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 flex items-center gap-3"
                  >
                    <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
                    <p className="text-red-200">{error}</p>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Provider Info */}
              <div className="bg-white/5 rounded-lg p-4 text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Sparkles className="w-4 h-4 text-yellow-400" />
                  <span className="text-sm font-medium text-gray-300">
                    Powered by {aiProvider === 'gemini' ? 'Google Gemini Live API' : 'OpenAI Whisper + GPT-4'}
                  </span>
                </div>
                <p className="text-xs text-gray-400">
                  {aiProvider === 'gemini' 
                    ? 'Real-time streaming voice conversation with Google\'s latest AI model'
                    : 'Advanced speech recognition and natural language processing by OpenAI'
                  }
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
};

export default AIHub;
