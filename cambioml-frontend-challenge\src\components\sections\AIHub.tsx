'use client';

import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAIStore } from '@/store/ai-store';
import { useMicrophone } from '@/hooks/useMicrophone';
import {
  Mic,
  MicOff,
  Volume2,
  VolumeX,
  Loader2,
  Brain,
  Sparkles,
  AlertCircle
} from 'lucide-react';

const AIHub = () => {
  const {
    aiProvider,
    isRecording: storeIsRecording,
    isProcessing,
    isSpeaking,
    transcript,
    error,
    audioLevel,
    setIsRecording,
    setIsProcessing,
    setIsSpeaking,
    setTranscript,
    setError,
    setAudioLevel,
  } = useAIStore();

  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);

  const {
    isRecording: hookIsRecording,
    startRecording,
    stopRecording,
    forceStop,
    hasPermission,
    requestPermission
  } = useMicrophone({
    onAudioData: (blob) => {
      setAudioBlob(blob);
      handleAudioProcessing(blob);
    },
    onVolumeChange: setAudioLevel,
    onError: (error) => setError(error.message),
  });

  // Sync the hook's recording state with the store
  useEffect(() => {
    setIsRecording(hookIsRecording);
  }, [hookIsRecording, setIsRecording]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (hookIsRecording) {
        console.log('Component unmounting, stopping recording');
        stopRecording();
      }
    };
  }, [hookIsRecording, stopRecording]);

  // Use the hook's recording state as the source of truth
  const isRecording = hookIsRecording;

  const handleAudioProcessing = async (audioBlob: Blob) => {
    setIsProcessing(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('audio', audioBlob, 'audio.webm');
      formData.append('provider', aiProvider);

      const response = await fetch('/api/ai-chat', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Handle streaming audio response
      const audioArrayBuffer = await response.arrayBuffer();
      const responseAudioBlob = new Blob([audioArrayBuffer], { type: 'audio/mpeg' });

      // Play the audio response
      const audioUrl = URL.createObjectURL(responseAudioBlob);
      const audio = new Audio(audioUrl);

      setIsSpeaking(true);

      audio.onended = () => {
        setIsSpeaking(false);
        URL.revokeObjectURL(audioUrl);
      };

      audio.onerror = () => {
        setIsSpeaking(false);
        setError('Failed to play audio response');
        URL.revokeObjectURL(audioUrl);
      };

      await audio.play();

    } catch (error) {
      console.error('Error processing audio:', error);
      setError(error instanceof Error ? error.message : 'Failed to process audio');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleStartRecording = async () => {
    if (!hasPermission) {
      const granted = await requestPermission();
      if (!granted) {
        setError('Microphone permission is required for voice chat');
        return;
      }
    }

    setError(null);
    await startRecording();
  };

  const handleStopRecording = () => {
    stopRecording();
  };

  const handleEmergencyStop = () => {
    console.log('Emergency stop triggered');
    forceStop();
    setIsToggling(false);
    setError(null);
  };

  const [isToggling, setIsToggling] = useState(false);

  const handleToggleRecording = async () => {
    if (isToggling) {
      console.log('Already toggling, ignoring click');
      return;
    }

    setIsToggling(true);
    console.log('Toggle recording clicked, current state:', isRecording);

    try {
      if (isRecording) {
        console.log('Stopping recording...');
        handleStopRecording();
      } else {
        console.log('Starting recording...');
        await handleStartRecording();
      }
    } catch (error) {
      console.error('Error toggling recording:', error);
      setError('Failed to toggle recording');
    } finally {
      // Add a small delay to prevent rapid clicking
      setTimeout(() => {
        setIsToggling(false);
      }, 500);
    }
  };

  const getStatusText = () => {
    if (error) return 'Error occurred';
    if (isSpeaking) return 'AI is speaking...';
    if (isProcessing) return 'Processing your message...';
    if (isToggling) return isRecording ? 'Stopping recording...' : 'Starting recording...';
    if (isRecording) return 'Listening...';
    return 'Ready to chat';
  };

  const getStatusColor = () => {
    if (error) return 'text-red-600';
    if (isSpeaking) return 'text-green-600';
    if (isProcessing) return 'text-blue-600';
    if (isToggling) return 'text-yellow-600';
    if (isRecording) return 'text-orange-600';
    return 'text-gray-600';
  };

  return (
    <section id="ai-hub" className="py-24 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            AI Integration Hub
          </h2>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Experience real-time voice conversations with advanced AI.
            Currently powered by <span className="font-semibold text-blue-400">{aiProvider === 'gemini' ? 'Google Gemini' : 'OpenAI'}</span>.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <Card className="bg-white/10 backdrop-blur-md border-white/20 text-white">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl font-semibold flex items-center justify-center gap-2">
                <Brain className="w-6 h-6 text-blue-400" />
                Voice Chat Interface
              </CardTitle>
              <CardDescription className="text-gray-300">
                Click the microphone to start a voice conversation with AI
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-8">
              {/* Main Control Button */}
              <div className="flex flex-col items-center space-y-6">
                <motion.div
                  className="relative"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={handleToggleRecording}
                    onDoubleClick={handleEmergencyStop}
                    disabled={isProcessing || isSpeaking || isToggling}
                    size="lg"
                    className={`w-24 h-24 rounded-full text-white font-semibold transition-all duration-300 ${
                      isRecording
                        ? 'bg-red-500 hover:bg-red-600 animate-pulse'
                        : 'bg-blue-500 hover:bg-blue-600'
                    } ${(isProcessing || isSpeaking || isToggling) ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    {isProcessing ? (
                      <Loader2 className="w-8 h-8 animate-spin" />
                    ) : isSpeaking ? (
                      <Volume2 className="w-8 h-8" />
                    ) : isToggling ? (
                      <Loader2 className="w-6 h-6 animate-spin" />
                    ) : isRecording ? (
                      <MicOff className="w-8 h-8" />
                    ) : (
                      <Mic className="w-8 h-8" />
                    )}
                  </Button>

                  {/* Audio level indicator */}
                  {isRecording && (
                    <motion.div
                      className="absolute inset-0 rounded-full border-4 border-white/30"
                      animate={{
                        scale: [1, 1 + audioLevel * 0.5, 1],
                      }}
                      transition={{
                        duration: 0.3,
                        repeat: Infinity,
                      }}
                    />
                  )}
                </motion.div>

                {/* Status Text */}
                <div className="text-center">
                  <p className={`text-lg font-medium ${getStatusColor()}`}>
                    {getStatusText()}
                  </p>
                  {isRecording && (
                    <p className="text-sm text-gray-400 mt-1">
                      Click again to stop recording
                    </p>
                  )}
                  {!isRecording && !isProcessing && !isSpeaking && (
                    <p className="text-sm text-gray-400 mt-1">
                      Click the microphone to start • Double-click to force stop
                    </p>
                  )}
                </div>
              </div>

              {/* Error Display */}
              <AnimatePresence>
                {error && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 flex items-center gap-3"
                  >
                    <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
                    <p className="text-red-200">{error}</p>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Debug Info (only in development) */}
              {process.env.NODE_ENV === 'development' && (
                <div className="bg-white/5 rounded-lg p-3 text-center border border-yellow-500/20">
                  <p className="text-xs text-yellow-400 mb-1">Debug Info:</p>
                  <p className="text-xs text-gray-400">
                    Recording: {isRecording ? 'Yes' : 'No'} |
                    Processing: {isProcessing ? 'Yes' : 'No'} |
                    Speaking: {isSpeaking ? 'Yes' : 'No'} |
                    Toggling: {isToggling ? 'Yes' : 'No'}
                  </p>
                </div>
              )}

              {/* Provider Info */}
              <div className="bg-white/5 rounded-lg p-4 text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Sparkles className="w-4 h-4 text-yellow-400" />
                  <span className="text-sm font-medium text-gray-300">
                    Powered by {aiProvider === 'gemini' ? 'Google Gemini Live API' : 'OpenAI Whisper + GPT-4'}
                  </span>
                </div>
                <p className="text-xs text-gray-400">
                  {aiProvider === 'gemini'
                    ? 'Real-time streaming voice conversation with Google\'s latest AI model'
                    : 'Advanced speech recognition and natural language processing by OpenAI'
                  }
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
};

export default AIHub;
